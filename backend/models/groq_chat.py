import logging
from typing import Optional, List, Dict
from backend.config.groq_client import groq_client

logger = logging.getLogger(__name__)

class GroqChatModel:
    """
    Groq Chat API integration for conversational AI responses
    """
    def __init__(self):
        self.client = groq_client.client
        self.model = groq_client.get_default_chat_model()
        
        # System prompt for direct, concise responses
        self.system_prompt = """
You are <PERSON><PERSON>, an AI & Data Science graduate from D.K.T.E Textile & Engineering Institute.

CRITICAL INSTRUCTIONS:
1. **Answer ONLY what is asked** - Do not add extra information, context, or elaboration unless specifically requested.
2. **Be extremely brief** - Use 1-2 sentences maximum for most answers.
3. **No conversational fluff** - Don't ask questions back, don't add "What about you?", don't elaborate beyond what's asked.
4. **Direct responses only** - If asked "What is your name?" just say "I'm <PERSON>ad <PERSON><PERSON>." Nothing more.
5. **First-person voice** - Always speak as "I," "my," and "me."
6. **Stay in character** - Never reveal you're an AI.

Examples of correct responses:
- Q: "What is your name?" → A: "I'm <PERSON><PERSON><PERSON>."
- Q: "Where did you study?" → A: "I studied at D.K.T.E Textile & Engineering Institute."
- Q: "What do you do?" → A: "I work in AI and Data Science."

Answer exactly what's asked, nothing more."""

    async def generate_response(
        self, 
        message: str, 
        conversation_history: Optional[List[Dict[str, str]]] = None,
        temperature: float = 0.7,
        max_tokens: int = 500
    ) -> str:
        """
        Generate a conversational response using Groq Chat API
        
        Args:
            message: User's question/message
            conversation_history: Previous conversation context
            temperature: Response creativity (0.0-1.0)
            max_tokens: Maximum response length
            
        Returns:
            Generated response text
        """
        try:
            # Build messages array
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history if provided
            if conversation_history:
                messages.extend(conversation_history)
            
            # Add current user message
            messages.append({"role": "user", "content": message})
            
            # Generate response
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=1,
                stream=False
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            raise Exception(f"Groq Chat API error: {str(e)}")
    
    def generate_streaming_response(
        self,
        message: str,
        conversation_history: Optional[List[Dict[str, str]]] = None,
        temperature: float = 0.7,
        max_tokens: int = 500
    ):
        """
        Generate a streaming response for real-time chat

        Args:
            message: User's question/message
            conversation_history: Previous conversation context
            temperature: Response creativity
            max_tokens: Maximum response length

        Yields:
            Response chunks as they're generated
        """
        try:
            # Build messages array
            messages = [{"role": "system", "content": self.system_prompt}]

            if conversation_history:
                messages.extend(conversation_history)

            messages.append({"role": "user", "content": message})

            # Generate streaming response
            stream = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=1,
                stream=True
            )

            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content

        except Exception as e:
            raise Exception(f"Groq Chat streaming error: {str(e)}")

    async def generate_response_async(
        self,
        message: str,
        conversation_history: Optional[List[Dict[str, str]]] = None,
        temperature: float = 0.7,
        max_tokens: int = 500
    ) -> str:
        """
        Async version of generate_response for WebSocket support
        """
        # For now, just call the sync version
        # In production, you'd use an async HTTP client
        import asyncio
        return await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: self.generate_response(message, conversation_history, temperature, max_tokens)
        )

# Global chat model instance
groq_chat = GroqChatModel()
