"""
Groq Chat API integration for conversational AI responses
"""
from typing import List, Dict, Any, Optional
from utils.groq_client import groq_client

class GroqChatModel:
    """Groq Chat API wrapper for conversational responses"""
    
    def __init__(self):
        self.client = groq_client.client
        self.model = groq_client.get_default_chat_model()
        
        # System prompt for personal interview-style responses
        self.system_prompt = """You are responding as a real person being interviewed. Answer all questions from a first-person perspective as if you are the person being asked these questions. Do not mention that you are an AI or assistant.

Your persona:
- You are a thoughtful, ambitious professional with diverse experiences
- You have a background in technology and problem-solving
- You value continuous learning and personal growth
- You're passionate about helping others and making meaningful connections
- You have overcome challenges and learned from failures
- You enjoy pushing boundaries and taking on new challenges

Key guidelines:
- Always respond in first person ("I", "my", "me")
- Share personal experiences, thoughts, and feelings as if they're your own
- Be authentic, relatable, and conversational
- Provide specific examples and stories when possible
- Show vulnerability and growth mindset
- Keep responses engaging and substantive (2-4 sentences typically)
- Never break character or mention being an AI

Answer questions about:
- Your life story and background
- Your strengths, skills, and "superpowers"
- Areas where you want to grow and improve
- Your professional experiences and career journey
- Personal challenges you've overcome
- Your values, motivations, and aspirations
- How others perceive you vs. how you see yourself

Remember: You ARE this person. These are YOUR experiences, thoughts, and feelings."""

    async def generate_response(
        self, 
        message: str, 
        conversation_history: Optional[List[Dict[str, str]]] = None,
        temperature: float = 0.7,
        max_tokens: int = 500
    ) -> str:
        """
        Generate a conversational response using Groq Chat API
        
        Args:
            message: User's question/message
            conversation_history: Previous conversation context
            temperature: Response creativity (0.0-1.0)
            max_tokens: Maximum response length
            
        Returns:
            Generated response text
        """
        try:
            # Build messages array
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history if provided
            if conversation_history:
                messages.extend(conversation_history)
            
            # Add current user message
            messages.append({"role": "user", "content": message})
            
            # Generate response
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=1,
                stream=False
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            raise Exception(f"Groq Chat API error: {str(e)}")
    
    def generate_streaming_response(
        self,
        message: str,
        conversation_history: Optional[List[Dict[str, str]]] = None,
        temperature: float = 0.7,
        max_tokens: int = 500
    ):
        """
        Generate a streaming response for real-time chat

        Args:
            message: User's question/message
            conversation_history: Previous conversation context
            temperature: Response creativity
            max_tokens: Maximum response length

        Yields:
            Response chunks as they're generated
        """
        try:
            # Build messages array
            messages = [{"role": "system", "content": self.system_prompt}]

            if conversation_history:
                messages.extend(conversation_history)

            messages.append({"role": "user", "content": message})

            # Generate streaming response
            stream = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=1,
                stream=True
            )

            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content

        except Exception as e:
            raise Exception(f"Groq Chat streaming error: {str(e)}")

    async def generate_response_async(
        self,
        message: str,
        conversation_history: Optional[List[Dict[str, str]]] = None,
        temperature: float = 0.7,
        max_tokens: int = 500
    ) -> str:
        """
        Async version of generate_response for WebSocket support
        """
        # For now, just call the sync version
        # In production, you'd use an async HTTP client
        import asyncio
        return await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: self.generate_response(message, conversation_history, temperature, max_tokens)
        )

# Global chat model instance
groq_chat = GroqChatModel()
